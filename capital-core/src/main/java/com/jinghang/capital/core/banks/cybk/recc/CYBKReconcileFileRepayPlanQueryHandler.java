package com.jinghang.capital.core.banks.cybk.recc;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.core.banks.cybk.enums.CYBKCreditStatus;
import com.jinghang.capital.core.banks.cybk.enums.CYBKReccFileType;
import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.repository.CYBKReconcileFileRepository;
import com.jinghang.capital.core.vo.recc.ReccResultVo;
import com.jinghang.capital.core.vo.recc.ReccType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Component
public class CYBKReconcileFileRepayPlanQueryHandler implements CYBKReccQueryHandler{
    private static final Logger logger = LoggerFactory.getLogger(CYBKReconcileFilePlanQueryHandler.class);
    @Autowired
    private CYBKReconcileFileRepository cybkReconcileFileRepository;
    @Override
    public ReccResultVo query(LocalDate date) {
        ReccResultVo reccResultVo = new ReccResultVo();
        logger.info("查询资方还款对账文件参数 channel: {}, date: {}, fileType: {}", BankChannel.CYBK.name(), date, CYBKReccFileType.REPAYMENT_FILE.name());
        List<CYBKReconcileFile> fileList = cybkReconcileFileRepository.findByChannelAndFileDateAndReccType(BankChannel.CYBK.name(), date, CYBKReccFileType.REPAYMENT_FILE.name());
        logger.info("查询资方还款对账文件数量:{}", fileList.size());
        boolean isValid = Optional.of(fileList)
                .filter(list -> !list.isEmpty())
                .map(list -> list.stream().anyMatch(file -> CYBKCreditStatus.SUCCESS.getCode().equals(file.getReccState())))
                .orElse(false);

        if ( isValid ) {
            reccResultVo.setStatus(ProcessStatus.SUCCESS);
            reccResultVo.setActReccTime(LocalDateTime.now());
        } else {
            reccResultVo.setStatus(ProcessStatus.FAIL);
            reccResultVo.setFailMsg("对资还款对账文件未完成或失败");
        }
        return reccResultVo;
    }

    @Override
    public ReccType getReccType() {
        return ReccType.REPAY_PLAN;
    }
}
