package com.jinghang.capital.core.banks.cybk.enums;


import com.jinghang.capital.core.enums.Education;

import java.util.Arrays;

/**
 * 最高学位转换
 *
 * <AUTHOR>
 * @date 2023/8/27
 */
public enum CYBKEduDegree {
    ZERO("1", "名誉博士", Education.DOCTOR),
    TWO("2", "博士", Education.DOCTOR),
    THREE("3", "硕士", Education.MASTER),
    FOUR("4", "学士", Education.COLLEGE),
    FIVE("5", "无", Education.UNKNOWN),
    SIX("9", "未知", Education.UNKNOWN);

    private final String code;
    private final String desc;
    private final Education education;

    CYBKEduDegree(String code, String desc, Education education) {
        this.code = code;
        this.desc = desc;
        this.education = education;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Education getEducation() {
        return education;
    }

    public static String getCodeByEducation(Education education) {
        return Arrays.stream(values()).filter(l -> education.equals(l.education)).findFirst().orElse(SIX).getCode();
    }

    public static String getEduDegreeCodeByEducation(Education education) {
        switch (education) {
            case MASTER:
                return THREE.getCode();
            case COLLEGE:
                return FOUR.getCode();
            case JUNIOR_COLLEGE,HIGH_SCHOOL,JUNIOR_HIGH_SCHOOL:
                return FIVE.getCode();
            case UNKNOWN:
                return SIX.getCode();
            default:
                return SIX.getCode();
        }
    }
}
