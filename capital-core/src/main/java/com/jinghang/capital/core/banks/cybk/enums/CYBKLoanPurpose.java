package com.jinghang.capital.core.banks.cybk.enums;


import com.jinghang.capital.api.dto.LoanPurpose;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/27
 */
public enum CYBKLoanPurpose {
    SHOPPING("JDJJ", "家电家居", LoanPurpose.SHOPPING),
    DECORATION("ZXZF", "装修租房", LoanPurpose.DECORATION),
    TOUR("WLYL", "文旅娱乐", LoanPurpose.TOUR),
    MARRIAGE("JTTX", "交通通讯", LoanPurpose.TRANSPORTATION),
    EDUCATION("JYFY", "教育抚育", LoanPurpose.EDUCATION),
    HEALTH("YLBJ", "医疗保健", LoanPurpose.HEALTH),
    OTHER("OTH", "家电家居", LoanPurpose.OTHER);

    private final String code;
    private final String desc;

    private final LoanPurpose loanPurpose;

    CYBKLoanPurpose(String code, String desc, LoanPurpose loanPurpose) {
        this.code = code;
        this.desc = desc;
        this.loanPurpose = loanPurpose;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }

    public static CYBKLoanPurpose getEnumByLoanPurpose(LoanPurpose loanPurpose) {
        return Arrays.stream(values()).filter(l -> loanPurpose.equals(l.loanPurpose)).findFirst().orElse(OTHER);
    }
}
