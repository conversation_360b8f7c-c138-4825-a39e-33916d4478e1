package com.jinghang.capital.core.banks.cybk.enums;


import com.jinghang.capital.core.enums.Relation;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/27
 */
public enum CYBKContactType {
    ONE("01", "父母", Relation.PARENTS),
    TWO("02", "子女", Relation.CHILDREN),
    THREE("03", "同事", Relation.COLLEAGUE),
    FIVE("05", "朋友", Relation.FRIEND),
    EIGHT("08", "兄弟姐妹", Relation.SIBLING),
    NINE("09", "配偶", Relation.SPOUSE),
    TWELVE("99", "其他", Relation.UNKNOWN);
    private final String code;
    private final String desc;
    private final Relation relation;

    CYBKContactType(String code, String desc, Relation relation) {
        this.code = code;
        this.desc = desc;
        this.relation = relation;
    }

    public static String getCodeByRelation(Relation relation) {
        return Arrays.stream(values()).filter(l -> relation.equals(l.relation)).findFirst().orElseThrow().code;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Relation getRelation() {
        return relation;
    }
}
