package com.maguo.loan.cash.flow.job.baofu;

import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BaoFuSettlementStateCons;
import com.maguo.loan.cash.flow.config.BaoFuSettlementMerchantConfigManage;
import com.maguo.loan.cash.flow.entity.BfSettlementNotify;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.BfSettlementNotifyRecordRepository;
import com.maguo.loan.cash.flow.service.baofu.BaoFuSettlementService;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 宝付结算款定时任务
 */
@Component
@JobHandler("baoFuSettlementJob")
public class BaoFuSettlementJob  extends AbstractJobHandler {

    @Autowired
    private BfSettlementNotifyRecordRepository bfSettlementNotifyRecordRepository;
    @Autowired
    @Qualifier("settlementTaskExecutor")
    private ThreadPoolTaskExecutor settlementTaskExecutor;
    @Autowired
    private BaoFuSettlementService baoFuSettlementService;
    @Autowired
    private BaoFuSettlementMerchantConfigManage baoFuSettlementMerchantConfigManage;

    private static final Logger logger = LoggerFactory.getLogger(AbstractJobHandler.class);

    @Override
    public void doJob(JobParam jobParam) {
        logger.info("baoFuSettlementJob jobParam:{}", JsonUtil.toJsonString(jobParam));
        List<BfSettlementNotify>  settlementNotifies =  bfSettlementNotifyRecordRepository.findByFinishStatus(BaoFuSettlementStateCons.NOTIFY_PROCESSING);
        for (BfSettlementNotify bfSettlementNotify : settlementNotifies) {
            settlementTaskExecutor.execute(() -> {
                try {
                    baoFuSettlementService.processSettlement(bfSettlementNotify.getServerTransId());
                } catch (Exception e) {
                    logger.error("serverTransId【{}】结算流程处理异常", bfSettlementNotify.getServerTransId(),e);
                }
            });
        }
    }
}
