package com.maguo.loan.cash.flow.convert;

import com.jinghang.ppd.api.dto.collection.PpdReduceApplyRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.PpdReduceApplyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CollectionConvert {
    CollectionConvert INSTANCE = Mappers.getMapper(CollectionConvert.class);


    PpdReduceApplyDTO toVo(PpdReduceApplyRequest ppdReduceApplyRequest);

}
