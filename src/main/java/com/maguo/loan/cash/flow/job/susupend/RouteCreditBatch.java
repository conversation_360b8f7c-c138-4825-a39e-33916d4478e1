package com.maguo.loan.cash.flow.job.susupend;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.dto.ProcessDTO;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.service.OrderService;
import com.xxl.job.core.handler.annotation.JobHandler;
import groovy.lang.Tuple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/10
 * 资金路由
 */
@Component
@JobHandler("routeCredit")
public class RouteCreditBatch extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(RouteCreditBatch.class);

    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private CapitalConfigRepository capitalConfigRepository;
    @Override
    public void doJob(JobParam jobParam) {
        logger.info("routeCredit jobParam:{}", JsonUtil.toJsonString(jobParam));
        if (jobParam != null && jobParam.getOrderId() != null) {
            String orderId = jobParam.getOrderId();
            Order order = orderRepository.findById(orderId).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
            orderService.orderRoute(order);
        } else if (jobParam != null && jobParam.getFlowChannel() != null) {
            FlowChannel flowChannel = jobParam.getFlowChannel();
            List<Order> orders = orderRepository.findByOrderStateAndFlowChannel(OrderState.SUSPENDED, flowChannel);
            if (CollectionUtil.isEmpty(orders)) {
                logger.warn("routeCredit被挂起的订单数量为0, flowChannel: {}", flowChannel);
                return;
            }
            logger.warn("routeCredit 被挂起的订单数量为{},开始处理, flowChannel: {}", orders.size(), flowChannel);
            for (Order order : orders) {
                orderService.orderRoute(order);
            }
        } else {
            //查询被挂起的订单
            String time =  LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            //获取到当期任务时间，去定位到需要激活的渠道流量，资方
            List<ProcessDTO> list = capitalConfigRepository.findByTime(time).stream()
                .map(map -> new ProcessDTO(
                    (String) map.get("flowChannel"),
                    (String) map.get("bankChannel")
                ))
                .collect(Collectors.toList());
            if(CollectionUtil.isEmpty(list)){return;}
            for(ProcessDTO  dto:list) {
                FlowChannel flowChannel= FlowChannel.valueOf(dto.getFlowChannel());
                BankChannel bankChannel= BankChannel.valueOf(dto.getBankChannel());
                List<String> orderIds = orderRepository.findIdsByOrderState(OrderState.SUSPENDED,flowChannel, bankChannel);
                logger.info("routeCredit 订单挂起数量为{}", orderIds.size());
                for (String orderId : orderIds) {
                    try {
                        Order order = orderRepository.findById(orderId).orElseThrow();
                        if (OrderState.SUSPENDED != order.getOrderState()) {
                            logger.info("routeCredit 订单[{}]状态为[{}],不激活挂起", orderId, order.getOrderState());
                            continue;
                        }
                        orderService.orderRoute(order);
                    } catch (Exception e) {
                        logger.error("routeCredit 订单[{}]挂起激活异常:", orderId, e);
                    }
                }
            }
        }
        logger.info("routeCredit end");
    }

}
