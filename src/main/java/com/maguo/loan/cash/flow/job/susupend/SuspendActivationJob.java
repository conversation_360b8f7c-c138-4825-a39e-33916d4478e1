package com.maguo.loan.cash.flow.job.susupend;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.dto.ProcessDTO;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.service.common.loan.LoanCommonService;

import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gale
 * @Classname Suspend
 * @Description 挂起激活任务
 * @Date 2024/1/6 14:00
 */

@Component
@JobHandler("suspendActivationJob")
public class SuspendActivationJob extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(SuspendActivationJob.class);

    @Autowired
    private LoanRepository loanRepository;

    private static final Integer PAGE_NUM = 0;
    private static final Integer PAGE_SIZE = 1000;

    @Autowired
    private LoanCommonService loanCommonService;

    public static final int LOCK_WAIT_SECOND = 3;

    public static final int LOCK_RELEASE_SECOND = 8;

    @Autowired
    private CapitalConfigRepository capitalConfigRepository;
    @Override
    public void doJob(JobParam jobParam) {
        logger.info("挂起激活任务开始执行,jobParam:{}", JsonUtil.toJsonString(jobParam));

        List<String> loanIds = Optional.ofNullable(jobParam).map(JobParam::getLoanIds).orElse(null);
        if (CollectionUtil.isNotEmpty(loanIds)) {
            for (String loanId : loanIds) {
                loanCommonService.suspendActive(loanId);
            }
            return;
        }


        //查询被挂起的订单
        String time =  LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        //获取到当期任务时间，去定位到需要激活的渠道流量，资方
        List<ProcessDTO> list = capitalConfigRepository.findByLoanTime(time).stream()
            .map(map -> new ProcessDTO(
                (String) map.get("flowChannel"),
                (String) map.get("bankChannel")
            ))
            .collect(Collectors.toList());
        for (ProcessDTO dto : list) {
            //开始循环的id，每次循环后更新
            String id = "";
            while (true) {
                PageRequest pageRequest = PageRequest.of(PAGE_NUM, PAGE_SIZE, Sort.by(Sort.Direction.ASC, "id"));
                //查询挂起的借据
                FlowChannel flowChannel = FlowChannel.valueOf(dto.getFlowChannel());
                BankChannel bankChannel = BankChannel.valueOf(dto.getBankChannel());
                logger.info("flowChannel:{},bankChannel:{}",flowChannel.name(),bankChannel.name());
                List<Loan> loans = loanRepository.findSuspendLoan(id, pageRequest, flowChannel, bankChannel);
                if (CollectionUtils.isEmpty(loans)) {
                    logger.info("挂起激活任务开始执行完成");
                    break;
                }
                for (Loan loan : loans) {
                    loanCommonService.suspendActive(loan.getId());
                }
                //重置最大的id
                id = Objects.requireNonNull(CollectionUtils.lastElement(loans)).getId();
            }
        }
    }
}
