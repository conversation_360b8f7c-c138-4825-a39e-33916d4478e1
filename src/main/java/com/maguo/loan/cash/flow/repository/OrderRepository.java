package com.maguo.loan.cash.flow.repository;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.enums.AmountType;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface OrderRepository extends JpaRepository<Order, String> {

    @Query("select o from Order o where o.outerOrderId  = ?1")
    Order findByOuterOrderId(String partnerOrderNo);

    @Query("select o.id from Order o where o.orderState = 'AUDIT_PASS' and o.applyTime <= ?1")
    List<String> findShouldCloseOrders(LocalDateTime applyTime);

    @Query("select o.id from Order o where o.orderState = 'SUSPENDED' and o.applyTime <= ?1 and o.flowChannel = ?2")
    List<String> findCloseSuspendedOrders(LocalDateTime applyTime, FlowChannel flowChannel);

    List<Order> findByFlowChannelAndOrderStateInAndLoanTimeBetween(FlowChannel channel, List<OrderState> orderStates,
                                                                   LocalDateTime startTime, LocalDateTime endTime);

    Order findTopByUserIdAndOrderStateOrderByCreatedTimeDesc(String userId, OrderState state);

    List<Order> findAllByUserIdAndFlowChannel(String userId, FlowChannel channel);

    List<Order> findAllByUserIdAndFlowChannelIn(String userId, List<FlowChannel> channels);

    List<Order> findAllByUserIdAndOrderStateAndFlowChannel(String userId, OrderState orderState, FlowChannel channel);

    List<Order> findAllByOpenIdAndFlowChannel(String openId, FlowChannel channel);

    Optional<Order> findFirstByOpenIdAndFlowChannelOrderByCreatedTimeDesc(String openId, FlowChannel channel);

    List<Order> findAllByOpenIdAndOrderStateAndFlowChannel(String openId, OrderState orderState, FlowChannel channel);

    List<Order> findAllByOrderStateAndFlowChannelAndLoanTimeBetween(OrderState orderState, FlowChannel channel, LocalDateTime startDay, LocalDateTime endDay);

    Order findByRiskId(String riskId);

    /**
     * 根据外部订单id和流量渠道
     *
     * @param outerOrderId 外部订单id
     * @param channel      流量渠道
     * @return 订单
     */
    Order findTopByOuterOrderIdAndFlowChannel(String outerOrderId, FlowChannel channel);

    Order findTopByOuterOrderIdAndFlowChannelAndApplyChannel(String outerOrderId, FlowChannel channel, String applyChannel);

    @Query("select o from Order o where (o.outerOrderId=?1 or o.id=?1) and o.flowChannel=?2")
    Order findTopByIdOrOuterOrderIdAndFlowChannel(String orderId, FlowChannel channel);

    @Query("select o from Order o where (o.outerOrderId=?1 or o.id=?1) and o.orderState=?2")
    Order findTopByIdOrOuterOrderIdAndOrderState(String orderId, OrderState state);

    Order findTopByOuterOrderIdAndFlowChannelInOrderByCreatedTimeDesc(String outOrderId, List<FlowChannel> channels);

    Order findOrderById(String id);

    int countByUserIdAndOrderStateIn(String userId, List<OrderState> orderStates);

    int countByUserIdAndFlowChannelInAndOrderState(String userId, List<FlowChannel> flowChannels, OrderState orderState);

    int countByCertNoAndFlowChannelInAndOrderState(String certNo, List<FlowChannel> flowChannels, OrderState orderState);

    int countByUserIdAndOrderStateInAndIdNotAndFlowChannel(String userId, List<OrderState> orderStates, String orderId,FlowChannel flowChannel);

    List<Order> findByUserIdAndOrderStateInAndIdNot(String userId, List<OrderState> orderStates, String id);

    int countByCertNoAndFlowChannelAndOrderStateIn(String certNo,FlowChannel flowChannel, List<OrderState> orderStates);

    @Query("select o.id from Order o where o.orderState = ?1 and o.flowChannel=?2 and o.bankChannel= ?3 ")
    List<String> findIdsByOrderState(OrderState orderState,FlowChannel flowChannel,BankChannel bankChannel);

    List<Order> findByOrderStateAndFlowChannel(OrderState orderState, FlowChannel flowChannel);

    Order findTopByUserIdOrderByCreatedTimeDesc(String userId);

    Optional<Order> findTopByUserIdAndFlowChannelOrderByCreatedTimeDesc(String userId, FlowChannel flowChannel);

    List<Order> findAllByUserIdAndOrderStateAndFlowChannelIn(String userId, OrderState orderState, List<FlowChannel> flowChannel);

    boolean existsByUserIdAndOrderStateAndFlowChannelIn(String userId, OrderState orderState, List<FlowChannel> flowChannel);

    @Query("select o from Order o where o.userId=?1 and (o.amountType!=?2 or o.amountType is null) and o.orderState not in ?3")
    List<Order> findAllByUserIdAndAmountTypeNotAndOrderStateNotIn(String userId, AmountType amountType, List<OrderState> orderStates);

    List<Order> findAllByUserId(String id);

    List<Order> findAllByUserIdIn(List<String> userIdList);

    List<Order> findByUserIdOrderByCreatedTimeDesc(String userId);

    Order findTopByOuterOrderId(String outerOrderId);

    List<Order> findByOrderStateAndOrderSubmitStateAndCreatedTimeBetween(OrderState orderState,
                                                                         WhetherState orderSubmitState, LocalDateTime startTime,
                                                                         LocalDateTime endTime);

    List<Order> findByOrderStateAndOrderSubmitStateAndRightsMarkingAndApproveRightsForceAndUpdatedTimeBetween(OrderState orderState,
                                                                                                              WhetherState orderSubmitState,
                                                                                                              WhetherState rightsMarking,
                                                                                                              WhetherState approveRightsForce,
                                                                                                              LocalDateTime startTime,
                                                                                                              LocalDateTime endTime);

    List<Order> findAllByIdIn(List<String> orderIds);
    Order findByOuterOrderIdAndFlowChannelAndOrderState(String outerOrderId, FlowChannel flowChannel, OrderState orderState);

    Order findByOuterOrderIdAndFlowChannel(String outerOrderId, FlowChannel flowChannel);

    Order findByUserIdAndFlowChannelAndOrderStateIn(String userId, FlowChannel flowChannel, List<OrderState> orderState);

    @Query("select case when count(o) > 0 THEN TRUE ELSE FALSE END from Order o "
        + "where o.userId=?1 and o.bankChannel=?3 and o.orderState=?4 and (o.amountType!=?2 or o.amountType is null)")
    boolean existsByUserIdAndAmountTypeNotAndBankChannelAndOrderState(String userId, AmountType amountType, BankChannel bankChannel, OrderState orderState);

    @Query("select case when count(o) > 0 THEN TRUE ELSE FALSE END from Order o "
        + "where o.userId=?1 and o.orderState=?2 and (o.amountType!=?3 or o.amountType is null)")
    boolean existsOrderByUserIdAndOrderStateAndAmountTypeNot(String userId, OrderState orderState, AmountType amountType);

    Order findTopByUserIdAndAmountTypeAndOrderStateInOrderByApplyTimeDesc(String userId, AmountType amountType, List<OrderState> onOrderStates);

    @Query("select o from Order o where o.userId=?1 and (o.amountType!=?2 or o.amountType is null) and o.orderState in ?3 order by o.applyTime desc limit 1")
    Order findTopByUserIdAndAmountTypeNotAndOrderStateInOrderByApplyTimeDesc(String userId, AmountType amountType, List<OrderState> onOrderStates);

    int countByUserIdAndAmountTypeAndOrderStateIn(String userId, AmountType amountType, List<OrderState> onOrderStates);

    @Query("select count(o) from Order o where o.userId=?1 and (o.amountType!=?2 or o.amountType is null) and o.orderState in ?3")
    int countByUserIdAndAmountTypeNotAndOrderStateIn(String userId, AmountType amountType, List<OrderState> onOrderStates);

    List<Order> findAllByUserIdAndAmountTypeAndOrderStateIn(String userId, AmountType amountType, List<OrderState> onOrderStates);

    @Query("select o from Order o where o.userId=?1 and (o.amountType!=?2 or o.amountType is null) and o.orderState in ?3")
    List<Order> findAllByUserIdAndAmountTypeNotAndOrderStateIn(String userId, AmountType amountType, List<OrderState> onOrderStates);

    Order findTopByUserIdAndAmountTypeOrderByCreatedTimeDesc(String userId, AmountType amountType);

    @Query("select o from Order o where o.userId=?1 and (o.amountType!=?2 or o.amountType is null) order by o.createdTime desc limit 1")
    Order findTopByUserIdAndAmountTypeNotOrderByCreatedTimeDesc(String userId, AmountType amountType);

    List<Order> findAllByUserIdAndOrderStateIn(String userId, List<OrderState> loanPass);

    @Query("select o from Order o where o.userId=?1 and (o.amountType != ?2 or o.amountType is null) and o.orderState in ?3 and o.id != ?4")
    List<Order> findAllByUserIdAndAmountTypeNotAndOrderStateInAndIdNot(String userId, AmountType amountType, List<OrderState> qhOnOrderStates, String orderId);

    Order findTopByCertNoAndFlowChannelAndLoanCardIdIsNotNullOrderByUpdatedTimeDesc(String certNo, FlowChannel flowChannel);

    @Query("select o from Order o where  o.orderState = ?1 and o.certNo = ?2 and o.applyTime > ?3 ")
    List<Order> queryThirtyDayCreditFailRecord(OrderState creditFail, String certNo, LocalDateTime failCreditDate);

    @Query("""
            SELECT o FROM Order o
                WHERE o.orderState = ?1
                AND o.certNo = ?2
                AND o.applyTime > ?3
                AND o.flowChannel = ?4
                AND o.bankChannel = ?5
        """)
    List<Order> queryThirtyDayCreditFailRecordLX(OrderState creditFail, String certNo, LocalDateTime failCreditDate,FlowChannel flowChannel,BankChannel bankChannel);

    Order findByOuterLoanNoAndFlowChannel(String outerLoanNo,FlowChannel flowChannel);

    @Query(nativeQuery = true, value = """
        SELECT
            COALESCE(COUNT(CASE WHEN order_state = 'CLEAR' THEN 1 END), 0) AS reapy_his_stl_num,
            COALESCE(COUNT(CASE WHEN order_state = 'CLEAR' AND updated_time >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)THEN 1 END), 0) AS reapy_lst1d_stl_num,
            COALESCE(COUNT(CASE WHEN order_state = 'CLEAR' AND updated_time >= DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)THEN 1 END), 0) AS reapy_lst3d_stl_num,
            COALESCE(COUNT(CASE WHEN order_state = 'CLEAR' AND updated_time >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)THEN 1 END), 0) AS reapy_lst7d_stl_num,
            COALESCE(DATEDIFF(CURRENT_DATE(),MAX(CASE WHEN order_state = 'CLEAR' THEN updated_time END)), 0) AS repay_lst_payoff_days
        FROM `order` WHERE user_id = :userId GROUP BY user_id
        """)
    Map<String, Object> findUserRepaySummary(@Param("userId") String userId);
}
