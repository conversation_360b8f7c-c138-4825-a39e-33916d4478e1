package com.maguo.loan.cash.flow.repository;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface CustomRepayRecordRepository extends JpaRepository<CustomRepayRecord, String> {

    // 获取指定时间段内所有还款流水计划
    List<CustomRepayRecord> findByRepaidDateBetweenAndRepayState( LocalDateTime startOfDay, LocalDateTime endOfDay, ProcessState loanState );
    List<CustomRepayRecord> findAllByLoanIdAndPeriod(String loanId, Integer period);

    Optional<CustomRepayRecord> findByLoanIdAndPeriodAndRepayState(String loanId, Integer period, ProcessState repayState);

    Optional<CustomRepayRecord> findTopByLoanIdAndPeriodOrderByCreatedTimeDesc(String loanId, Integer period);

    List<CustomRepayRecord> findByRepayStateAndRepaidDateBetween(ProcessState processState, LocalDateTime startDay, LocalDateTime endDay);

    CustomRepayRecord findByLoanIdAndPeriod(String loanId, Integer period);

    CustomRepayRecord findByOuterRepayNo(String repayNo);

    List<CustomRepayRecord> findByLoanId(String id);

    List<CustomRepayRecord> findByLoanIdAndRepayStateOrderByPeriodDesc(String id, ProcessState repayState);

    CustomRepayRecord findTopByLoanIdOrderByCreatedTimeDesc(String id);

    CustomRepayRecord findTopByLoanIdAndRepayStateOrderByCreatedTimeDesc(String id, ProcessState repayState);

    List<CustomRepayRecord> findByRepayApplyDateBetweenAndRepayStateIn(LocalDateTime start, LocalDateTime end, List<ProcessState> init);

    Optional<CustomRepayRecord> findByLoanIdAndRepayStateAndRepayPurposeOrderByCreatedTimeDesc(String loanId,
                                                                                               ProcessState repayState,
                                                                                               RepayPurpose repayPurpose);
}
