/*
package com.maguo.loan.cash.flow.service.listener;

import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.push.impl.PushToMkServiceImpl;
import com.maguo.loan.cash.flow.service.push.impl.PushToMkThread;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

*/
/**
 * <AUTHOR>
 *//*

@Component
public class CustomerInfoPushMkListener {
    private static final Logger log = LoggerFactory.getLogger(CustomerInfoPushMkListener.class);

    @Resource
    private PushToMkThread pushToMkThread;


    @RabbitListener(queues = RabbitConfig.Queues.SHM_PUSH_CUS_INFO)
    public void ListenerQueue(Message message, Channel channel) throws IOException {

        String messageBody;
        try {
            messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("监听推送信息处理数据有误", e);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.error("错误消息签收，防止消息堆积...");
            return;
        }

        log.info("监听数据准备进行推送业务处理------{}", messageBody);

        // 3. 处理单条或批量LoanId
        if (messageBody.contains(",")) {
            // 批量处理
            Arrays.stream(messageBody.split(","))
                .forEach(this::pushInfo);
        } else {
            // 单条处理
            pushInfo(messageBody);
        }
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }

    private void pushInfo(String loanId) {
        pushToMkThread.shmPush(loanId);
    }

}
*/
