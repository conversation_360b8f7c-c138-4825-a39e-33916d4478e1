package com.maguo.loan.cash.flow.dto;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;

public class ProcessDTO {

    private String bankChannel;
    @Enumerated(EnumType.STRING)
    @Column(name = "flow_channel")
    private String flowChannel;

    public ProcessDTO(String flowChannel, String bankChannel) {
        this.flowChannel = flowChannel;
        this.bankChannel = bankChannel;
    }

    public String getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(String bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(String flowChannel) {
        this.flowChannel = flowChannel;
    }
}
