package com.maguo.loan.cash.flow.controller;

import com.alibaba.fastjson.JSON;
import com.maguo.loan.cash.flow.config.BaoFuSettlementMerchantConfigManage;
import com.maguo.loan.cash.flow.entity.BaoFuWithdrawNotifyData;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.service.baofu.BaoFuSettlementService;
import com.maguo.loan.cash.flow.util.RsaCodingUtil;
import com.maguo.loan.cash.flow.util.SecurityUtil;
import com.zsjz.third.part.baofoo.vo.BaoFuSettlementInfo;
import com.zsjz.third.part.baofoo.vo.NotifyDataEntity;
import com.zsjz.third.part.baofoo.vo.ReciveEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@RestController
@RequestMapping("/baofu")
@Configuration
public class BaoFuSettlementNotifyController {
    @Autowired
    private BaoFuSettlementService baoFuSettlementService;
    @Autowired
    private BaoFuSettlementMerchantConfigManage configManage;

    private static final Logger logger = LoggerFactory.getLogger(AbstractJobHandler.class);

    /**
     * 宝付结算异步通知
     * @param Recives
     * @return
     */
    @PostMapping("/settlementNotify")
    public String settlementNotify(@RequestParam Map<String, Object> Recives) {
        logger.info("宝付结算异步通知:data={}" , Recives);
        try {
            if (Recives == null) throw new Exception("Parms is Null");
            if (Recives.keySet().size() <= 0) throw new Exception("No Parms");
            ReciveEntity recive = JSON.parseObject(JSON.toJSONString(Recives), ReciveEntity.class);
            logger.info("宝付结算异步通知转换参数：{}" , recive);
            if (recive.getData_content() == null) throw new Exception("datacontent is null");
            String ReslutString = RsaCodingUtil.decryptByPubCerText(recive.getData_content(), configManage.getActiveMerchantConfig().getPublicKey());
            if (StringUtils.isEmpty(ReslutString)) throw new Exception("解密失败");
            ReslutString = SecurityUtil.Base64Decode(ReslutString);
            NotifyDataEntity dataResult = JSON.parseObject(ReslutString, NotifyDataEntity.class);
            logger.info("宝付结算数据json:{}：" , JSON.toJSONString(dataResult.getData()));
            BaoFuSettlementInfo baoFuSettlementInfo = JSON.parseObject( dataResult.getData(), BaoFuSettlementInfo.class);
            baoFuSettlementService.process(baoFuSettlementInfo);
            return "OK";
        } catch (Exception ex) {
            logger.error("宝付结算异步通知异常：data={}", JSON.toJSONString(Recives), ex);
            return "";
        }
    }


    /**
     * 宝付提现异步通知
     * @param Recives
     * @return
     */
    @PostMapping("/cashWithdrawalNotify")
    public String cashWithdrawalNotify(@RequestParam Map<String, Object> Recives) {
        logger.info("宝付提现异步通知:data={}" , Recives);
        try {
            if (Recives == null) throw new Exception("Parms is Null");
            if (Recives.keySet().size() <= 0) throw new Exception("No Parms");
            ReciveEntity recive = JSON.parseObject(JSON.toJSONString(Recives), ReciveEntity.class);
            logger.info("宝付提现异步通知转换参数：{}" , recive);
            if (recive.getData_content() == null) throw new Exception("datacontent is null");
            String ReslutString = RsaCodingUtil.decryptByPubCerText(recive.getData_content(), configManage.getActiveMerchantConfig().getPublicKey());
            if (StringUtils.isEmpty(ReslutString)) throw new Exception("解密失败");
            ReslutString = SecurityUtil.Base64Decode(ReslutString);
            NotifyDataEntity dataResult = JSON.parseObject(ReslutString, NotifyDataEntity.class);
            logger.info("宝付提现数据json:{}：" , JSON.toJSONString(dataResult.getData()));
            BaoFuWithdrawNotifyData baoFuWithdrawNotifyData = JSON.parseObject( dataResult.getData(), BaoFuWithdrawNotifyData.class);
            if(baoFuSettlementService.baoFuWithdrawalNotify(baoFuWithdrawNotifyData)){
               // 业务处理中，需要继续通知
                return "";
            };
            return "ok";
        } catch (Exception ex) {
            logger.error("宝付提现异步通知异常：data={}", JSON.toJSONString(Recives), ex);
            return "";
        }
    }
}
