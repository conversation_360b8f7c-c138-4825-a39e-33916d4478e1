package com.maguo.loan.cash.flow.util;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.config.LvXinConfig;
import com.maguo.loan.cash.flow.config.LvXinNewSFTPConfig;
import com.maguo.loan.cash.flow.entrance.ppd.config.PpdConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.util.SftpUtils
 * @作者 Mr.sandman
 * @时间 2025/05/29 16:51
 */
@Component
public class SftpUtils {

    private static final Logger logger = LoggerFactory.getLogger(SftpUtils.class);

    @Autowired
    private LvXinConfig lvXinConfig;
    @Autowired
    private LvXinNewSFTPConfig lvXinNewSFTPConfig;

    @Autowired
    private PpdConfig ppdConfig;

    // 绿信对账文件使用
    public void uploadStreamToSftp(ByteArrayOutputStream stream, String fileName, String remoteDir) throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(lvXinConfig.getSftpUserName(), lvXinConfig.getSftpIp(), lvXinConfig.getSftpPort());
        session.setPassword(lvXinConfig.getSftpPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
        sftp.connect();

        try {
            // 递归创建目录
            confirmPath(remoteDir, sftp);

            // 上传文件
            byte[] fileBytes = stream.toByteArray();
            InputStream inputStream = new ByteArrayInputStream(fileBytes);
            sftp.put(inputStream, fileName);

            logger.info("文件上传成功, 路径：{}/{}", remoteDir, fileName);

        } finally {
            sftp.disconnect();
            session.disconnect();
        }
    }

    // 绿信新sftp对账文件使用
    public void uploadStreamToNewSftp(ByteArrayOutputStream stream, String fileName, String remoteDir) throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(lvXinNewSFTPConfig.getSftpUserName(), lvXinNewSFTPConfig.getSftpIp(), lvXinNewSFTPConfig.getSftpPort());
        session.setPassword(lvXinNewSFTPConfig.getSftpPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
        sftp.connect();

        try {
            // 递归创建目录
            confirmPath(remoteDir, sftp);

            // 上传文件
            byte[] fileBytes = stream.toByteArray();
            InputStream inputStream = new ByteArrayInputStream(fileBytes);
            sftp.put(inputStream, fileName);

            logger.info("文件上传成功, 路径：{}/{}", remoteDir, fileName);

        } finally {
            sftp.disconnect();
            session.disconnect();
        }
    }

    // 拍拍贷对账文件使用
    public void uploadStreamToPPDSftp(ByteArrayOutputStream stream, String fileName, String remoteDir) throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(ppdConfig.getSftpUser(), ppdConfig.getSftpHost(), ppdConfig.getSftpPort());
        session.setPassword(ppdConfig.getSftpPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
        sftp.connect();

        try {
            // 递归创建目录
            confirmPath(remoteDir, sftp);

            // 上传文件
            byte[] fileBytes = stream.toByteArray();
            InputStream inputStream = new ByteArrayInputStream(fileBytes);
            sftp.put(inputStream, fileName);

            logger.info("文件上传成功, 路径：{}/{}", remoteDir, fileName);

        } finally {
            sftp.disconnect();
            session.disconnect();
        }
    }
    // 拍拍贷-湖消对账文件使用
    public void uploadStreamToPPDSftpHx(ByteArrayOutputStream stream, String fileName, String remoteDir) throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(ppdConfig.getSftpHxUser(), ppdConfig.getSftpHxHost(), ppdConfig.getSftpHxPort());
        session.setPassword(ppdConfig.getSftpHxPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
        sftp.connect();

        try {
            // 递归创建目录
            confirmPath(remoteDir, sftp);

            // 上传文件
            byte[] fileBytes = stream.toByteArray();
            InputStream inputStream = new ByteArrayInputStream(fileBytes);
            sftp.put(inputStream, fileName);

            logger.info("文件上传成功, 路径：{}/{}", remoteDir, fileName);

        } finally {
            sftp.disconnect();
            session.disconnect();
        }
    }

    // 绿信协议文件使用
    public void uploadStreamToLvXinSftp(InputStream inputStream, String fileName, String remoteDir) throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(lvXinConfig.getSftpUserName(), lvXinConfig.getSftpIp(), lvXinConfig.getSftpPort());
        session.setPassword(lvXinConfig.getSftpPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
        sftp.connect();

        try {
            logger.info("开始递归创建文件, 文件路径：{}, sftp相关信息：{}", remoteDir, sftp);
            // 递归创建目录
            confirmPath(remoteDir, sftp);
            logger.info("文件创建成功, 文件路径：{}", remoteDir);

            logger.info("开始上传文件, 文件名：{}, 文件路径：{}, sftp相关信息：{}", fileName, remoteDir, sftp);
            // 上传文件
            sftp.put(inputStream, fileName);
            logger.info("文件上传成功, 路径：{}/{}", remoteDir, fileName);

        } finally {
            sftp.disconnect();
            session.disconnect();
        }
    }

    // 绿信新协议文件使用
    public void uploadStreamToLvXinNewSftp(InputStream inputStream, String fileName, String remoteDir) throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(lvXinNewSFTPConfig.getSftpUserName(), lvXinNewSFTPConfig.getSftpIp(), lvXinNewSFTPConfig.getSftpPort());
        session.setPassword(lvXinNewSFTPConfig.getSftpPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
        sftp.connect();

        try {
            logger.info("开始递归创建文件, 文件路径：{}, sftp相关信息：{}", remoteDir, sftp);
            // 递归创建目录
            confirmPath(remoteDir, sftp);
            logger.info("文件创建成功, 文件路径：{}", remoteDir);

            logger.info("开始上传文件, 文件名：{}, 文件路径：{}, sftp相关信息：{}", fileName, remoteDir, sftp);
            // 上传文件
            sftp.put(inputStream, fileName);
            logger.info("文件上传成功, 路径：{}/{}", remoteDir, fileName);

        } finally {
            sftp.disconnect();
            session.disconnect();
        }
    }

    // 拍拍贷协议文件使用
    public void uploadStreamToPPCJDLSftp(InputStream inputStream, String fileName, String remoteDir) throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(ppdConfig.getSftpUser(), ppdConfig.getSftpHost(), ppdConfig.getSftpPort());
        session.setPassword(ppdConfig.getSftpPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
        sftp.connect();

        try {
            logger.info("开始递归创建文件, 文件路径：{}, sftp相关信息：{}", remoteDir, sftp);
            // 递归创建目录
            confirmPath(remoteDir, sftp);
            logger.info("文件创建成功, 文件路径：{}", remoteDir);

            logger.info("开始上传文件, 文件名：{}, 文件路径：{}, sftp相关信息：{}", fileName, remoteDir, sftp);
            // 上传文件
            sftp.put(inputStream, fileName);
            logger.info("文件上传成功, 路径：{}/{}", remoteDir, fileName);

        } finally {
            sftp.disconnect();
            session.disconnect();
        }
    }

    // 拍拍贷协议文件使用HYBK
    public void uploadStreamToPPCJDLSftpHx(InputStream inputStream, String fileName, String remoteDir) throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(ppdConfig.getSftpHxUser(), ppdConfig.getSftpHxHost(), ppdConfig.getSftpHxPort());
        session.setPassword(ppdConfig.getSftpHxPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
        sftp.connect();

        try {
            logger.info("开始递归创建文件, 文件路径：{}, sftp相关信息：{}", remoteDir, sftp);
            // 递归创建目录
            confirmPath(remoteDir, sftp);
            logger.info("文件创建成功, 文件路径：{}", remoteDir);

            logger.info("开始上传文件, 文件名：{}, 文件路径：{}, sftp相关信息：{}", fileName, remoteDir, sftp);
            // 上传文件
            sftp.put(inputStream, fileName);
            logger.info("文件上传成功, 路径：{}/{}", remoteDir, fileName);

        } finally {
            sftp.disconnect();
            session.disconnect();
        }
    }

    private void confirmPath(String remoteDir, ChannelSftp sftp) throws SftpException {
        String[] folders = remoteDir.split("/");
        String path = "";
        for (String folder : folders) {
            if (folder.isEmpty()) {
                continue;
            }
            path += "/" + folder;
            try {
                sftp.cd(path);
            } catch (SftpException e) {
                sftp.mkdir(path);
                sftp.cd(path);
            }
        }
    }

    // ppd-hx检查文件是否存在的专用方法
    public void fileExistsHx(String[] remoteFilePath) throws Exception {
        Session session = null;
        ChannelSftp sftp = null;
        String index = "";
        try {
            session = createSessionHx();
            logger.info("host:{},port:{},sftp连接成功",  ppdConfig.getSftpHxHost(), ppdConfig.getSftpHxPort());
            sftp = (ChannelSftp) session.openChannel("sftp");
            sftp.connect();
            for (String filePath : remoteFilePath) {
                index = filePath;
                logger.info("开始校验影像信息文件fileExists-remoteFilePath:{}", ppdConfig.getSftpHxCreditApplyDownloadPath() + filePath);
                // 这边一定时文件 且文件不存在直接抛异常 不需要判断更多
                sftp.stat(ppdConfig.getSftpHxCreditApplyDownloadPath() + filePath);
                logger.info("sftp查看检查影像信息文件没有问题");
            }
            // 检查是否是文件（排除目录）
        } catch (Exception e) {
            closeResources(sftp, session);
            throw new SftpException(10, index + "文件不存在", e);
        } finally {
            closeResources(sftp, session);
        }
    }

    // 检查文件是否存在的专用方法
    public void fileExists(String[] remoteFilePath) throws Exception {
        Session session = null;
        ChannelSftp sftp = null;
        String index = "";
        try {
            session = createSession();
            sftp = (ChannelSftp) session.openChannel("sftp");
            sftp.connect();
            for (String filePath : remoteFilePath) {
                index = filePath;
                logger.info("fileExists-remoteFilePath:{}",ppdConfig.getSftpCreditApplyDownloadPath() + filePath);
                // 这边一定时文件 且文件不存在直接抛异常 不需要判断更多
                sftp.stat(ppdConfig.getSftpCreditApplyDownloadPath() + filePath);
            }
            // 检查是否是文件（排除目录）
        } catch (Exception e) {
            closeResources(sftp, session);
            throw new SftpException(10,index + "文件不存在",e);
        }finally {
            closeResources(sftp, session);
        }
    }

    // 在 SftpUtils 类中添加以下方法
    public InputStream downloadAsStream(String remoteFilePath) throws Exception {
        Session session = null;
        ChannelSftp sftp = null;
        try {
            session = createSession();
            sftp = (ChannelSftp) session.openChannel("sftp");
            sftp.connect();
            logger.info("remoteFilePath:{}",remoteFilePath);
            // 启动新线程处理下载和流传输
            byte[] bytes = sftp.get(remoteFilePath).readAllBytes();
            return new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            System.err.println("⚠️ 文件流下载失败: " + e.getMessage());
            closeResources(sftp, session);
            throw e;
        }finally {
            closeResources(sftp, session);
        }
    }

    public InputStream downloadAsStreamHx(String remoteFilePath) throws Exception {
        Session session = null;
        ChannelSftp sftp = null;
        try {
            session = createSessionHx();
            sftp = (ChannelSftp) session.openChannel("sftp");
            sftp.connect();
            logger.info("remoteFilePath:{}",remoteFilePath);
            // 启动新线程处理下载和流传输
            byte[] bytes = sftp.get(remoteFilePath).readAllBytes();
            return new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            System.err.println("⚠️ 文件流下载失败: " + e.getMessage());
            closeResources(sftp, session);
            throw e;
        }finally {
            closeResources(sftp, session);
        }
    }
    // 复用之前创建的辅助方法
    private Session createSessionHx() throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(ppdConfig.getSftpHxUser(), ppdConfig.getSftpHxHost(), ppdConfig.getSftpHxPort());
        session.setPassword(ppdConfig.getSftpHxPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        logger.info("sftp-user:{},host:{},port:{},pwd:{}", ppdConfig.getSftpHxUser(), ppdConfig.getSftpHxHost(), ppdConfig.getSftpHxPort(), ppdConfig.getSftpHxPassword());
        session.connect();
        logger.info("host:{},port:{},sftp连接成功",  ppdConfig.getSftpHxHost(), ppdConfig.getSftpHxPort());
        return session;
    }


    private Session createSession() throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(ppdConfig.getSftpUser(), ppdConfig.getSftpHost(), ppdConfig.getSftpPort());
        session.setPassword(ppdConfig.getSftpPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        logger.info("sftp-user:{},host:{},port:{},pwd:{}",ppdConfig.getSftpUser(),ppdConfig.getSftpHost(),ppdConfig.getSftpPort(),ppdConfig.getSftpPassword());
        session.connect();
        return session;
    }

    private void closeResources(ChannelSftp sftp, Session session) {
        if (sftp != null && sftp.isConnected()) {
            sftp.disconnect();
        }
        if (session != null && session.isConnected()) {
            session.disconnect();
        }
    }

}
