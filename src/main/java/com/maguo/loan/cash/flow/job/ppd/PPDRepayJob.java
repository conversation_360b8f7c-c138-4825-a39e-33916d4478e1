package com.maguo.loan.cash.flow.job.ppd;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.recc.ReccApplyDto;
import com.jinghang.capital.api.dto.recc.ReccResultDto;
import com.jinghang.capital.api.dto.recc.ReccType;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.ppd.vo.RepaymentInfoVo;
import com.maguo.loan.cash.flow.entrance.ppd.config.PpdConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.remote.core.FinReccService;
import com.maguo.loan.cash.flow.service.PPDReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @公司 中数金智(上海)有限公司
 * @作者 Mr.sandman
 * @时间 2025/06/23 11:30
 */
@Component
@JobHandler("ppdRepayJob")
public class PPDRepayJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PPDRepayJob.class);

    @Autowired
    private PPDReconService ppdReconService;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private FinReccService finReccService;
    @Autowired
    private PpdConfig ppdConfig;

    private static final String FILE_PREFIX = "RC_";
    private static final String FILE_SUFFIX = ".txt";
    private static final String OK_SUFFIX = ".ok";
    private static final String SLASH = "/";

    @Override
    public void doJob(JobParam jobParam) {
        logger.info("生成拍拍还款文件txt文件开始");
        if (jobParam.getBankChannel() == null) {
            logger.error("资方通道为空，请在定时任务中填写资方通道");
            throw new NullPointerException("bankChannel is null");
        }
        logger.info("当前JobParam参数: {}", JsonUtil.toJsonString(jobParam));
        try {
            // 日期处理
            LocalDate localDate = jobParam != null && jobParam.getStartDate() != null
                ? jobParam.getStartDate()
                : LocalDate.now().minusDays(1);
            String yesterday = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = FILE_PREFIX + yesterday + FILE_SUFFIX;
            String okFileName = FILE_PREFIX + yesterday + OK_SUFFIX;

            String remoteDir = null;
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                 remoteDir = ppdConfig.getSftpRepayPath() + yesterday + SLASH;
            } else if ( jobParam.getBankChannel() == BankChannel.HXBK ) {
                remoteDir = ppdConfig.getSftpHxRepayPath() + yesterday + SLASH;
            }
            logger.info("远程目录路径: {}", remoteDir);
            ReccApplyDto reccApplyDto = new ReccApplyDto();
            reccApplyDto.setChannel(jobParam.getBankChannel());
            reccApplyDto.setReccDay(localDate);
            reccApplyDto.setReccType(ReccType.REPAY);
            logger.info("拍拍还款文件txt上传,对资对账文件接口参数:{}", JsonUtil.toJsonString(reccApplyDto));
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                RestResult<ReccResultDto> query = finReccService.query(reccApplyDto);
                logger.info("拍拍还款文件txt上传,对资对账文件接口返回信息:{}", JsonUtil.toJsonString(query));
                if (!(query.isSuccess() && query.getData().getStatus() == ProcessStatus.SUCCESS)) {
                    logger.info("拍拍还款文件txt上传失败 对资对账文件接口调用失败:{}", query.getData().getFailMsg());
                    return;
                }
            }
            FlowChannel flowChannel = FlowChannel.getFlowChannel(jobParam.getChannel());
            logger.info("获取拍拍放款文件数据 时间: {}, 渠道: {}", localDate, flowChannel);
            // 获取数据
            List<RepaymentInfoVo> repaymentInfoVos = ppdReconService.getRepayDetailReconFile(localDate, flowChannel, jobParam.getBankChannel());
            logger.info("获取拍拍还款文件数据成功，数据量：{}", repaymentInfoVos.size());
            // 生成文件流
            ByteArrayOutputStream stream = generateCsvToStream(repaymentInfoVos);
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                // sftp上传
                sftpUtils.uploadStreamToPPDSftp(stream, fileName, remoteDir);
                // 上传.ok文件
                sftpUtils.uploadStreamToPPDSftp(new ByteArrayOutputStream(), okFileName, remoteDir);
                logger.info("拍拍还款文件上传成功，文件名：{}", fileName);
            } else if ( jobParam.getBankChannel() == BankChannel.HXBK ) {
                // sftp上传
                sftpUtils.uploadStreamToPPDSftpHx(stream, fileName, remoteDir);
                // 上传.ok文件
                sftpUtils.uploadStreamToPPDSftpHx(new ByteArrayOutputStream(), okFileName, remoteDir);
                logger.info("拍拍还款文件上传成功，文件名：{}", fileName);
            }

        } catch (Exception e) {
            logger.error("拍拍还款文件txt上传失败", e);
            e.printStackTrace();
        }
    }

    public ByteArrayOutputStream generateCsvToStream(List<RepaymentInfoVo> data) throws IOException {

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
        BufferedWriter bw = new BufferedWriter(writer);

        // 写数据
        for (RepaymentInfoVo repay : data) {
            bw.write(String.join("|", Arrays.asList(
                ppdReconService.safe(repay.getLoanReqNo()),
                ppdReconService.safe(repay.getSourceCode()),
                ppdReconService.safe(repay.getRepayNo()),
                ppdReconService.safe(repay.getRepayMode()),
                ppdReconService.safe(repay.getRepayType()),
                ppdReconService.safe(repay.getRepayDate()),
                ppdReconService.safe(String.valueOf(repay.getRepayTerm())),
                ppdReconService.safe(repay.getRepayAmount()),
                ppdReconService.safe(repay.getRepayPrincipal()),
                ppdReconService.safe(repay.getRepayInterest()),
                ppdReconService.safe(repay.getRepayOverdue()),
                ppdReconService.safe(repay.getRepayPoundage()),
                ppdReconService.safe(repay.getRepayLateFee()))));
            bw.newLine();
        }

        bw.flush();
        return outputStream;
    }

}
